import type { OAuth2Provider } from "../index.js";
export declare class FortyTwo implements OAuth2Provider {
    private client;
    private clientSecret;
    constructor(clientId: string, clientSecret: string, redirectURI: string);
    createAuthorizationURL(state: string, options?: {
        scopes?: string[];
    }): Promise<URL>;
    validateAuthorizationCode(code: string): Promise<FortyTwoTokens>;
}
export interface FortyTwoTokens {
    accessToken: string;
    accessTokenExpiresAt: Date;
}
