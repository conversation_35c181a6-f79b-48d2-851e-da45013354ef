import { OAuth2Client } from "oslo/oauth2";
import { TimeSpan, createDate } from "oslo";
const authorizeEndpoint = "https://www.patreon.com/oauth2/authorize";
const tokenEndpoint = "https://www.patreon.com/api/oauth2/token";
export class Patreon {
    client;
    clientSecret;
    constructor(clientId, clientSecret, redirectURI) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI
        });
        this.clientSecret = clientSecret;
    }
    async createAuthorizationURL(state, options) {
        return await this.client.createAuthorizationURL({
            state,
            scopes: options?.scopes ?? []
        });
    }
    async validateAuthorizationCode(code) {
        const result = await this.client.validateAuthorizationCode(code, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
    async refreshAccessToken(refreshToken) {
        const result = await this.client.refreshAccessToken(refreshToken, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
}
