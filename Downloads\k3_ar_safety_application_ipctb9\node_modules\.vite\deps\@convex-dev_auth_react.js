"use client";
import {
  require_jsx_runtime
} from "./chunk-MJNCUEZK.js";
import {
  ConvexHttpClient,
  ConvexProviderWithAuth
} from "./chunk-FWMNHU4W.js";
import "./chunk-KJPOZADT.js";
import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@convex-dev/auth/dist/react/index.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_react3 = __toESM(require_react());

// node_modules/@convex-dev/auth/dist/react/client.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var ConvexAuthActionsContext = (0, import_react.createContext)(void 0);
var ConvexAuthInternalContext = (0, import_react.createContext)(void 0);
function useAuth() {
  return (0, import_react.useContext)(ConvexAuthInternalContext);
}
var ConvexAuthTokenContext = (0, import_react.createContext)(null);
var VERIFIER_STORAGE_KEY = "__convexAuthOAuthVerifier";
var JWT_STORAGE_KEY = "__convexAuthJWT";
var REFRESH_TOKEN_STORAGE_KEY = "__convexAuthRefreshToken";
var SERVER_STATE_FETCH_TIME_STORAGE_KEY = "__convexAuthServerStateFetchTime";
function AuthProvider({ client, serverState, onChange, storage, storageNamespace, replaceURL, children }) {
  const token = (0, import_react.useRef)((serverState == null ? void 0 : serverState._state.token) ?? null);
  const [isLoading, setIsLoading] = (0, import_react.useState)(token.current === null);
  const [tokenState, setTokenState] = (0, import_react.useState)(token.current);
  const verbose = client.verbose ?? false;
  const logVerbose = (0, import_react.useCallback)((message) => {
    if (verbose) {
      console.debug(`${(/* @__PURE__ */ new Date()).toISOString()} ${message}`);
    }
  }, [verbose]);
  const { storageSet, storageGet, storageRemove, storageKey } = useNamespacedStorage(storage, storageNamespace);
  const [isRefreshingToken, setIsRefreshingToken] = (0, import_react.useState)(false);
  const setToken = (0, import_react.useCallback)(async (args) => {
    const wasAuthenticated = token.current !== null;
    let newToken;
    if (args.tokens === null) {
      token.current = null;
      if (args.shouldStore) {
        await storageRemove(JWT_STORAGE_KEY);
        await storageRemove(REFRESH_TOKEN_STORAGE_KEY);
      }
      newToken = null;
    } else {
      const { token: value } = args.tokens;
      token.current = value;
      if (args.shouldStore) {
        const { refreshToken } = args.tokens;
        await storageSet(JWT_STORAGE_KEY, value);
        await storageSet(REFRESH_TOKEN_STORAGE_KEY, refreshToken);
      }
      newToken = value;
    }
    if (wasAuthenticated !== (newToken !== null)) {
      await (onChange == null ? void 0 : onChange());
    }
    setTokenState(newToken);
    setIsLoading(false);
  }, [storageSet, storageRemove]);
  (0, import_react.useEffect)(() => {
    const listener = async (e) => {
      if (isRefreshingToken) {
        e.preventDefault();
        const confirmationMessage = "Are you sure you want to leave? Your changes may not be saved.";
        e.returnValue = true;
        return confirmationMessage;
      }
    };
    browserAddEventListener("beforeunload", listener);
    return () => {
      browserRemoveEventListener("beforeunload", listener);
    };
  });
  (0, import_react.useEffect)(() => {
    const listener = (event) => {
      void (async () => {
        if (event.storageArea !== storage) {
          return;
        }
        if (event.key === storageKey(JWT_STORAGE_KEY)) {
          const value = event.newValue;
          logVerbose(`synced access token, is null: ${value === null}`);
          await setToken({
            shouldStore: false,
            tokens: value === null ? null : { token: value }
          });
        }
      })();
    };
    browserAddEventListener("storage", listener);
    return () => browserRemoveEventListener("storage", listener);
  }, [setToken]);
  const verifyCodeAndSetToken = (0, import_react.useCallback)(async (args) => {
    const { tokens } = await client.unauthenticatedCall("auth:signIn", "code" in args ? { params: { code: args.code }, verifier: args.verifier } : args);
    logVerbose(`retrieved tokens, is null: ${tokens === null}`);
    await setToken({ shouldStore: true, tokens: tokens ?? null });
    return tokens !== null;
  }, [client, setToken]);
  const signIn = (0, import_react.useCallback)(async (provider, args) => {
    const params = args instanceof FormData ? Array.from(args.entries()).reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {}) : args ?? {};
    const verifier = await storageGet(VERIFIER_STORAGE_KEY) ?? void 0;
    await storageRemove(VERIFIER_STORAGE_KEY);
    const result = await client.authenticatedCall("auth:signIn", { provider, params, verifier });
    if (result.redirect !== void 0) {
      const url = new URL(result.redirect);
      await storageSet(VERIFIER_STORAGE_KEY, result.verifier);
      if (window.location !== void 0) {
        window.location.href = url.toString();
      }
      return { signingIn: false, redirect: url };
    } else if (result.tokens !== void 0) {
      const { tokens } = result;
      logVerbose(`signed in and got tokens, is null: ${tokens === null}`);
      await setToken({ shouldStore: true, tokens });
      return { signingIn: result.tokens !== null };
    }
    return { signingIn: false };
  }, [client, setToken, storageGet]);
  const signOut = (0, import_react.useCallback)(async () => {
    try {
      await client.authenticatedCall("auth:signOut");
    } catch (error) {
    }
    logVerbose(`signed out, erasing tokens`);
    await setToken({ shouldStore: true, tokens: null });
  }, [setToken, client]);
  const fetchAccessToken = (0, import_react.useCallback)(async ({ forceRefreshToken }) => {
    if (forceRefreshToken) {
      const tokenBeforeLockAquisition = token.current;
      return await browserMutex(REFRESH_TOKEN_STORAGE_KEY, async () => {
        const tokenAfterLockAquisition = token.current;
        if (tokenAfterLockAquisition !== tokenBeforeLockAquisition) {
          logVerbose(`returning synced token, is null: ${tokenAfterLockAquisition === null}`);
          return tokenAfterLockAquisition;
        }
        const refreshToken = await storageGet(REFRESH_TOKEN_STORAGE_KEY) ?? null;
        if (refreshToken !== null) {
          setIsRefreshingToken(true);
          await verifyCodeAndSetToken({ refreshToken }).finally(() => {
            setIsRefreshingToken(false);
          });
          logVerbose(`returning retrieved token, is null: ${tokenAfterLockAquisition === null}`);
          return token.current;
        } else {
          setIsRefreshingToken(false);
          logVerbose(`returning null, there is no refresh token`);
          return null;
        }
      });
    }
    return token.current;
  }, [verifyCodeAndSetToken, signOut, storageGet]);
  const signingInWithCodeFromURL = (0, import_react.useRef)(false);
  (0, import_react.useEffect)(
    () => {
      if (storage === void 0) {
        throw new Error("`localStorage` is not available in this environment, set the `storage` prop on `ConvexAuthProvider`!");
      }
      const readStateFromStorage = async () => {
        const token2 = await storageGet(JWT_STORAGE_KEY) ?? null;
        logVerbose(`retrieved token from storage, is null: ${token2 === null}`);
        await setToken({
          shouldStore: false,
          tokens: token2 === null ? null : { token: token2 }
        });
      };
      if (serverState !== void 0) {
        const timeFetched = storageGet(SERVER_STATE_FETCH_TIME_STORAGE_KEY);
        const setTokensFromServerState = (timeFetched2) => {
          if (!timeFetched2 || serverState._timeFetched > +timeFetched2) {
            const { token: token2, refreshToken } = serverState._state;
            const tokens = token2 === null || refreshToken === null ? null : { token: token2, refreshToken };
            void storageSet(SERVER_STATE_FETCH_TIME_STORAGE_KEY, serverState._timeFetched.toString());
            void setToken({ tokens, shouldStore: true });
          } else {
            void readStateFromStorage();
          }
        };
        if (timeFetched instanceof Promise) {
          void timeFetched.then(setTokensFromServerState);
        } else {
          setTokensFromServerState(timeFetched);
        }
        return;
      }
      const code = typeof (window == null ? void 0 : window.location) !== "undefined" ? new URLSearchParams(window.location.search).get("code") : null;
      if (signingInWithCodeFromURL.current || code) {
        if (code && !signingInWithCodeFromURL.current) {
          signingInWithCodeFromURL.current = true;
          const url = new URL(window.location.href);
          url.searchParams.delete("code");
          void (async () => {
            await replaceURL(url.pathname + url.search + url.hash);
            await signIn(void 0, { code });
            signingInWithCodeFromURL.current = false;
          })();
        }
      } else {
        void readStateFromStorage();
      }
    },
    // Explicitly chosen dependencies.
    // This effect should mostly only run once
    // on mount.
    [client, storageGet]
  );
  const actions = (0, import_react.useMemo)(() => ({ signIn, signOut }), [signIn, signOut]);
  const isAuthenticated = tokenState !== null;
  const authState = (0, import_react.useMemo)(() => ({
    isLoading,
    isAuthenticated,
    fetchAccessToken
  }), [fetchAccessToken, isLoading, isAuthenticated]);
  return (0, import_jsx_runtime.jsx)(ConvexAuthInternalContext.Provider, { value: authState, children: (0, import_jsx_runtime.jsx)(ConvexAuthActionsContext.Provider, { value: actions, children: (0, import_jsx_runtime.jsx)(ConvexAuthTokenContext.Provider, { value: tokenState, children }) }) });
}
function useNamespacedStorage(peristentStorage, namespace) {
  const inMemoryStorage = useInMemoryStorage();
  const storage = (0, import_react.useMemo)(() => peristentStorage ?? inMemoryStorage(), [peristentStorage]);
  const escapedNamespace = namespace.replace(/[^a-zA-Z0-9]/g, "");
  const storageKey = (0, import_react.useCallback)((key) => `${key}_${escapedNamespace}`, [namespace]);
  const storageSet = (0, import_react.useCallback)((key, value) => storage.setItem(storageKey(key), value), [storage, storageKey]);
  const storageGet = (0, import_react.useCallback)((key) => storage.getItem(storageKey(key)), [storage, storageKey]);
  const storageRemove = (0, import_react.useCallback)((key) => storage.removeItem(storageKey(key)), [storage, storageKey]);
  return { storageSet, storageGet, storageRemove, storageKey };
}
function useInMemoryStorage() {
  const [inMemoryStorage, setInMemoryStorage] = (0, import_react.useState)({});
  return () => ({
    getItem: (key) => inMemoryStorage[key],
    setItem: (key, value) => {
      setInMemoryStorage((prev) => ({ ...prev, [key]: value }));
    },
    removeItem: (key) => {
      setInMemoryStorage((prev) => {
        const { [key]: _, ...rest } = prev;
        return rest;
      });
    }
  });
}
async function browserMutex(key, callback) {
  var _a;
  const lockManager = (_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.locks;
  return lockManager !== void 0 ? await lockManager.request(key, callback) : await manualMutex(key, callback);
}
function getMutexValue(key) {
  if (globalThis.__convexAuthMutexes === void 0) {
    globalThis.__convexAuthMutexes = {};
  }
  let mutex = globalThis.__convexAuthMutexes[key];
  if (mutex === void 0) {
    globalThis.__convexAuthMutexes[key] = {
      currentlyRunning: null,
      waiting: []
    };
  }
  mutex = globalThis.__convexAuthMutexes[key];
  return mutex;
}
function setMutexValue(key, value) {
  globalThis.__convexAuthMutexes[key] = value;
}
async function enqueueCallbackForMutex(key, callback) {
  const mutex = getMutexValue(key);
  if (mutex.currentlyRunning === null) {
    setMutexValue(key, {
      currentlyRunning: callback().finally(() => {
        const nextCb = getMutexValue(key).waiting.shift();
        getMutexValue(key).currentlyRunning = null;
        setMutexValue(key, {
          ...getMutexValue(key),
          currentlyRunning: nextCb === void 0 ? null : enqueueCallbackForMutex(key, nextCb)
        });
      }),
      waiting: []
    });
  } else {
    setMutexValue(key, {
      ...mutex,
      waiting: [...mutex.waiting, callback]
    });
  }
}
async function manualMutex(key, callback) {
  const outerPromise = new Promise((resolve, reject) => {
    const wrappedCallback = () => {
      return callback().then((v) => resolve(v)).catch((e) => reject(e));
    };
    void enqueueCallbackForMutex(key, wrappedCallback);
  });
  return outerPromise;
}
function browserAddEventListener(type, listener, options) {
  var _a;
  (_a = window.addEventListener) == null ? void 0 : _a.call(window, type, listener, options);
}
function browserRemoveEventListener(type, listener, options) {
  var _a;
  (_a = window.removeEventListener) == null ? void 0 : _a.call(window, type, listener, options);
}

// node_modules/@convex-dev/auth/dist/react/index.js
function useAuthActions() {
  return (0, import_react3.useContext)(ConvexAuthActionsContext);
}
function ConvexAuthProvider(props) {
  const { client, storage, storageNamespace, replaceURL, children } = props;
  const authClient = (0, import_react3.useMemo)(() => {
    var _a;
    return {
      authenticatedCall(action, args) {
        return client.action(action, args);
      },
      unauthenticatedCall(action, args) {
        return new ConvexHttpClient(client.address).action(action, args);
      },
      verbose: (_a = client.options) == null ? void 0 : _a.verbose
    };
  }, [client]);
  return (0, import_jsx_runtime2.jsx)(AuthProvider, { client: authClient, storage: storage ?? // Handle SSR, RN, Web, etc.
  // Pretend we always have storage, the component checks
  // it in first useEffect.
  (typeof window === "undefined" ? void 0 : window == null ? void 0 : window.localStorage), storageNamespace: storageNamespace ?? client.address, replaceURL: replaceURL ?? ((url) => {
    window.history.replaceState({}, "", url);
  }), children: (0, import_jsx_runtime2.jsx)(ConvexProviderWithAuth, { client, useAuth, children }) });
}
function useAuthToken() {
  return (0, import_react3.useContext)(ConvexAuthTokenContext);
}
export {
  ConvexAuthProvider,
  useAuthActions,
  useAuthToken
};
//# sourceMappingURL=@convex-dev_auth_react.js.map
