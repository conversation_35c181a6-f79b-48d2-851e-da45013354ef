import type { OAuth2Provider } from "../index.js";
export declare class Coinbase implements OAuth2Provider {
    private client;
    private clientSecret;
    constructor(clientId: string, clientSecret: string, options?: {
        redirectURI?: string;
    });
    createAuthorizationURL(state: string, options?: {
        scopes?: string[];
    }): Promise<URL>;
    validateAuthorizationCode(code: string): Promise<CoinbaseTokens>;
    refreshAccessToken(refreshToken: string): Promise<CoinbaseTokens>;
}
export interface CoinbaseTokens {
    accessToken: string;
    refreshToken: string;
    accessTokenExpiresAt: Date;
}
