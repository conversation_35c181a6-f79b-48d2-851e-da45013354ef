import { OAuth2Client } from "oslo/oauth2";
const authorizeEndpoint = "https://lichess.org/oauth";
const tokenEndpoint = "https://lichess.org/api/token";
export class Lichess {
    client;
    constructor(clientId, redirectURI) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI
        });
    }
    async createAuthorizationURL(state, codeVerifier, options) {
        return await this.client.createAuthorizationURL({
            state,
            scopes: options?.scopes ?? [],
            codeVerifier
        });
    }
    async validateAuthorizationCode(code, codeVerifier) {
        const result = await this.client.validateAuthorizationCode(code, {
            codeVerifier
        });
        const tokens = {
            accessToken: result.access_token
        };
        return tokens;
    }
}
