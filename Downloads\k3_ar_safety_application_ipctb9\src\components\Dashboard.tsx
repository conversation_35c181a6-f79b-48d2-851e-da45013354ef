import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function Dashboard() {
  const arStats = useQuery(api.ar.getARSessionStats, { timeRange: "week" });
  const recentReports = useQuery(api.reports.listReports, { status: "reported" });
  const hazards = useQuery(api.hazards.listHazards, {});

  if (arStats === undefined || recentReports === undefined || hazards === undefined) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const hazardsByRisk = hazards.reduce((acc, hazard) => {
    acc[hazard.riskLevel] = (acc[hazard.riskLevel] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const reportsBySeverity = recentReports.reduce((acc, report) => {
    acc[report.severity] = (acc[report.severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Safety Dashboard</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">🎥</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">AR Sessions</p>
              <p className="text-2xl font-semibold text-gray-900">{arStats.totalSessions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-2xl">⚠️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Hazards Detected</p>
              <p className="text-2xl font-semibold text-gray-900">{arStats.totalHazardsDetected}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-2xl">📋</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Open Reports</p>
              <p className="text-2xl font-semibold text-gray-900">{recentReports.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">🛡️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Hazards</p>
              <p className="text-2xl font-semibold text-gray-900">{hazards.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Hazards by Risk Level */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Hazards by Risk Level</h3>
          <div className="space-y-3">
            {Object.entries(hazardsByRisk).map(([risk, count]) => (
              <div key={risk} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    risk === "critical" ? "bg-red-500" :
                    risk === "high" ? "bg-orange-500" :
                    risk === "medium" ? "bg-yellow-500" :
                    "bg-green-500"
                  }`}></div>
                  <span className="text-sm font-medium text-gray-700 capitalize">{risk}</span>
                </div>
                <span className="text-sm font-semibold text-gray-900">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Reports by Severity */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Reports by Severity</h3>
          <div className="space-y-3">
            {Object.entries(reportsBySeverity).map(([severity, count]) => (
              <div key={severity} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    severity === "critical" ? "bg-red-500" :
                    severity === "high" ? "bg-orange-500" :
                    severity === "medium" ? "bg-yellow-500" :
                    "bg-green-500"
                  }`}></div>
                  <span className="text-sm font-medium text-gray-700 capitalize">{severity}</span>
                </div>
                <span className="text-sm font-semibold text-gray-900">{count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Reports */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Safety Reports</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {recentReports.slice(0, 5).map((report) => (
            <div key={report._id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">{report.title}</h4>
                  <p className="text-sm text-gray-600">{report.description}</p>
                  <div className="flex items-center mt-2 space-x-4">
                    <span className="text-xs text-gray-500">
                      📍 {report.location.area}
                    </span>
                    <span className="text-xs text-gray-500">
                      📅 {new Date(report._creationTime).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    report.severity === "critical" ? "bg-red-100 text-red-800" :
                    report.severity === "high" ? "bg-orange-100 text-orange-800" :
                    report.severity === "medium" ? "bg-yellow-100 text-yellow-800" :
                    "bg-green-100 text-green-800"
                  }`}>
                    {report.severity}
                  </span>
                  <span className="text-xs text-gray-500 capitalize">{report.reportType}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
