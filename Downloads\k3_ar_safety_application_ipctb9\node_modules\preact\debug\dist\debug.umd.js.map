{"version": 3, "file": "debug.umd.js", "sources": ["../src/check-props.js", "../src/component-stack.js", "../src/debug.js", "../src/constants.js", "../src/util.js", "../src/index.js"], "sourcesContent": ["const ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nlet loggedTypeFailures = {};\n\n/**\n * Reset the history of which prop type warnings have been logged.\n */\nexport function resetPropWarnings() {\n\tloggedTypeFailures = {};\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * Adapted from https://github.com/facebook/prop-types/blob/master/checkPropTypes.js\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n */\nexport function checkPropTypes(\n\ttypeSpecs,\n\tvalues,\n\tlocation,\n\tcomponentName,\n\tgetStack\n) {\n\tObject.keys(typeSpecs).forEach(typeSpecName => {\n\t\tlet error;\n\t\ttry {\n\t\t\terror = typeSpecs[typeSpecName](\n\t\t\t\tvalues,\n\t\t\t\ttypeSpecName,\n\t\t\t\tcomponentName,\n\t\t\t\tlocation,\n\t\t\t\tnull,\n\t\t\t\tReactPropTypesSecret\n\t\t\t);\n\t\t} catch (e) {\n\t\t\terror = e;\n\t\t}\n\t\tif (error && !(error.message in loggedTypeFailures)) {\n\t\t\tloggedTypeFailures[error.message] = true;\n\t\t\tconsole.error(\n\t\t\t\t`Failed ${location} type: ${error.message}${\n\t\t\t\t\t(getStack && `\\n${getStack()}`) || ''\n\t\t\t\t}`\n\t\t\t);\n\t\t}\n\t});\n}\n", "import { options, Fragment } from 'preact';\n\n/**\n * Get human readable name of the component/dom node\n * @param {import('./internal').VNode} vnode\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getDisplayName(vnode) {\n\tif (vnode.type === Fragment) {\n\t\treturn 'Fragment';\n\t} else if (typeof vnode.type == 'function') {\n\t\treturn vnode.type.displayName || vnode.type.name;\n\t} else if (typeof vnode.type == 'string') {\n\t\treturn vnode.type;\n\t}\n\n\treturn '#text';\n}\n\n/**\n * Used to keep track of the currently rendered `vnode` and print it\n * in debug messages.\n */\nlet renderStack = [];\n\n/**\n * Keep track of the current owners. An owner describes a component\n * which was responsible to render a specific `vnode`. This exclude\n * children that are passed via `props.children`, because they belong\n * to the parent owner.\n *\n * ```jsx\n * const Foo = props => <div>{props.children}</div> // div's owner is Foo\n * const Bar = props => {\n *   return (\n *     <Foo><span /></Foo> // Foo's owner is Bar, span's owner is Bar\n *   )\n * }\n * ```\n *\n * Note: A `vnode` may be hoisted to the root scope due to compiler\n * optimiztions. In these cases the `_owner` will be different.\n */\nlet ownerStack = [];\n\n/**\n * Get the currently rendered `vnode`\n * @returns {import('./internal').VNode | null}\n */\nexport function getCurrentVNode() {\n\treturn renderStack.length > 0 ? renderStack[renderStack.length - 1] : null;\n}\n\n/**\n * If the user doesn't have `@babel/plugin-transform-react-jsx-source`\n * somewhere in his tool chain we can't print the filename and source\n * location of a component. In that case we just omit that, but we'll\n * print a helpful message to the console, notifying the user of it.\n */\nlet showJsxSourcePluginWarning = true;\n\n/**\n * Check if a `vnode` is a possible owner.\n * @param {import('./internal').VNode} vnode\n */\nfunction isPossibleOwner(vnode) {\n\treturn typeof vnode.type == 'function' && vnode.type != Fragment;\n}\n\n/**\n * Return the component stack that was captured up to this point.\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getOwnerStack(vnode) {\n\tconst stack = [vnode];\n\tlet next = vnode;\n\twhile (next._owner != null) {\n\t\tstack.push(next._owner);\n\t\tnext = next._owner;\n\t}\n\n\treturn stack.reduce((acc, owner) => {\n\t\tacc += `  in ${getDisplayName(owner)}`;\n\n\t\tconst source = owner.__source;\n\t\tif (source) {\n\t\t\tacc += ` (at ${source.fileName}:${source.lineNumber})`;\n\t\t} else if (showJsxSourcePluginWarning) {\n\t\t\tconsole.warn(\n\t\t\t\t'Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons.'\n\t\t\t);\n\t\t}\n\t\tshowJsxSourcePluginWarning = false;\n\n\t\treturn (acc += '\\n');\n\t}, '');\n}\n\n/**\n * Setup code to capture the component trace while rendering. Note that\n * we cannot simply traverse `vnode._parent` upwards, because we have some\n * debug messages for `this.setState` where the `vnode` is `undefined`.\n */\nexport function setupComponentStack() {\n\tlet oldDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldRoot = options._root;\n\tlet oldVNode = options.vnode;\n\tlet oldRender = options._render;\n\n\toptions.diffed = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.pop();\n\t\t}\n\t\trenderStack.pop();\n\t\tif (oldDiffed) oldDiffed(vnode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\trenderStack.push(vnode);\n\t\t}\n\t\tif (oldDiff) oldDiff(vnode);\n\t};\n\n\toptions._root = (vnode, parent) => {\n\t\townerStack = [];\n\t\tif (oldRoot) oldRoot(vnode, parent);\n\t};\n\n\toptions.vnode = vnode => {\n\t\tvnode._owner =\n\t\t\townerStack.length > 0 ? ownerStack[ownerStack.length - 1] : null;\n\t\tif (oldVNode) oldVNode(vnode);\n\t};\n\n\toptions._render = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.push(vnode);\n\t\t}\n\n\t\tif (oldRender) oldRender(vnode);\n\t};\n}\n", "import { checkPropTypes } from './check-props';\nimport { options, Component } from 'preact';\nimport {\n\tELEMENT_NODE,\n\tDOCUMENT_NODE,\n\tDOCUMENT_FRAGMENT_NODE\n} from './constants';\nimport {\n\tgetOwnerStack,\n\tsetupComponentStack,\n\tgetCurrentVNode,\n\tgetDisplayName\n} from './component-stack';\nimport { assign, isNaN } from './util';\n\nconst isWeakMapSupported = typeof WeakMap == 'function';\n\n/**\n * @param {import('./internal').VNode} vnode\n * @returns {Array<string>}\n */\nfunction getDomChildren(vnode) {\n\tlet domChildren = [];\n\n\tif (!vnode._children) return domChildren;\n\n\tvnode._children.forEach(child => {\n\t\tif (child && typeof child.type === 'function') {\n\t\t\tdomChildren.push.apply(domChildren, getDomChildren(child));\n\t\t} else if (child && typeof child.type === 'string') {\n\t\t\tdomChildren.push(child.type);\n\t\t}\n\t});\n\n\treturn domChildren;\n}\n\n/**\n * @param {import('./internal').VNode} parent\n * @returns {string}\n */\nfunction getClosestDomNodeParentName(parent) {\n\tif (!parent) return '';\n\tif (typeof parent.type == 'function') {\n\t\tif (parent._parent == null) {\n\t\t\tif (parent._dom != null && parent._dom.parentNode != null) {\n\t\t\t\treturn parent._dom.parentNode.localName;\n\t\t\t}\n\t\t\treturn '';\n\t\t}\n\t\treturn getClosestDomNodeParentName(parent._parent);\n\t}\n\treturn /** @type {string} */ (parent.type);\n}\n\nexport function initDebug() {\n\tsetupComponentStack();\n\n\tlet hooksAllowed = false;\n\n\t/* eslint-disable no-console */\n\tlet oldBeforeDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldVnode = options.vnode;\n\tlet oldRender = options._render;\n\tlet oldCatchError = options._catchError;\n\tlet oldRoot = options._root;\n\tlet oldHook = options._hook;\n\tconst warnedComponents = !isWeakMapSupported\n\t\t? null\n\t\t: {\n\t\t\t\tuseEffect: new WeakMap(),\n\t\t\t\tuseLayoutEffect: new WeakMap(),\n\t\t\t\tlazyPropTypes: new WeakMap()\n\t\t\t};\n\tconst deprecations = [];\n\n\toptions._catchError = (error, vnode, oldVNode, errorInfo) => {\n\t\tlet component = vnode && vnode._component;\n\t\tif (component && typeof error.then == 'function') {\n\t\t\tconst promise = error;\n\t\t\terror = new Error(\n\t\t\t\t`Missing Suspense. The throwing component was: ${getDisplayName(vnode)}`\n\t\t\t);\n\n\t\t\tlet parent = vnode;\n\t\t\tfor (; parent; parent = parent._parent) {\n\t\t\t\tif (parent._component && parent._component._childDidSuspend) {\n\t\t\t\t\terror = promise;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// We haven't recovered and we know at this point that there is no\n\t\t\t// Suspense component higher up in the tree\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\ttry {\n\t\t\terrorInfo = errorInfo || {};\n\t\t\terrorInfo.componentStack = getOwnerStack(vnode);\n\t\t\toldCatchError(error, vnode, oldVNode, errorInfo);\n\n\t\t\t// when an error was handled by an ErrorBoundary we will nonetheless emit an error\n\t\t\t// event on the window object. This is to make up for react compatibility in dev mode\n\t\t\t// and thus make the Next.js dev overlay work.\n\t\t\tif (typeof error.then != 'function') {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tthrow e;\n\t\t}\n\t};\n\n\toptions._root = (vnode, parentNode) => {\n\t\tif (!parentNode) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined parent passed to render(), this is the second argument.\\n' +\n\t\t\t\t\t'Check if the element is available in the DOM/has the correct id.'\n\t\t\t);\n\t\t}\n\n\t\tlet isValid;\n\t\tswitch (parentNode.nodeType) {\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\tcase DOCUMENT_NODE:\n\t\t\t\tisValid = true;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tisValid = false;\n\t\t}\n\n\t\tif (!isValid) {\n\t\t\tlet componentName = getDisplayName(vnode);\n\t\t\tthrow new Error(\n\t\t\t\t`Expected a valid HTML node as a second argument to render.\tReceived ${parentNode} instead: render(<${componentName} />, ${parentNode});`\n\t\t\t);\n\t\t}\n\n\t\tif (oldRoot) oldRoot(vnode, parentNode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tlet { type } = vnode;\n\n\t\thooksAllowed = true;\n\n\t\tif (type === undefined) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined component passed to createElement()\\n\\n' +\n\t\t\t\t\t'You likely forgot to export your component or might have mixed up default and named imports' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type != null && typeof type == 'object') {\n\t\t\tif (type._children !== undefined && type._dom !== undefined) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Invalid type passed to createElement(): ${type}\\n\\n` +\n\t\t\t\t\t\t'Did you accidentally pass a JSX literal as JSX twice?\\n\\n' +\n\t\t\t\t\t\t`  let My${getDisplayName(vnode)} = ${serializeVNode(type)};\\n` +\n\t\t\t\t\t\t`  let vnode = <My${getDisplayName(vnode)} />;\\n\\n` +\n\t\t\t\t\t\t'This usually happens when you export a JSX literal and not the component.' +\n\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t'Invalid type passed to createElement(): ' +\n\t\t\t\t\t(Array.isArray(type) ? 'array' : type)\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\tvnode.ref !== undefined &&\n\t\t\ttypeof vnode.ref != 'function' &&\n\t\t\ttypeof vnode.ref != 'object' &&\n\t\t\t!('$$typeof' in vnode) // allow string refs when preact-compat is installed\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t`Component's \"ref\" property should be a function, or an object created ` +\n\t\t\t\t\t`by createRef(), but got [${typeof vnode.ref}] instead\\n` +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t}\n\n\t\tif (typeof vnode.type == 'string') {\n\t\t\tfor (const key in vnode.props) {\n\t\t\t\tif (\n\t\t\t\t\tkey[0] === 'o' &&\n\t\t\t\t\tkey[1] === 'n' &&\n\t\t\t\t\ttypeof vnode.props[key] != 'function' &&\n\t\t\t\t\tvnode.props[key] != null\n\t\t\t\t) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Component's \"${key}\" property should be a function, ` +\n\t\t\t\t\t\t\t`but got [${typeof vnode.props[key]}] instead\\n` +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Check prop-types if available\n\t\tif (typeof vnode.type == 'function' && vnode.type.propTypes) {\n\t\t\tif (\n\t\t\t\tvnode.type.displayName === 'Lazy' &&\n\t\t\t\twarnedComponents &&\n\t\t\t\t!warnedComponents.lazyPropTypes.has(vnode.type)\n\t\t\t) {\n\t\t\t\tconst m =\n\t\t\t\t\t'PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ';\n\t\t\t\ttry {\n\t\t\t\t\tconst lazyVNode = vnode.type();\n\t\t\t\t\twarnedComponents.lazyPropTypes.set(vnode.type, true);\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + `Component wrapped in lazy() is ${getDisplayName(lazyVNode)}`\n\t\t\t\t\t);\n\t\t\t\t} catch (promise) {\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + \"We will log the wrapped component's name once it is loaded.\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet values = vnode.props;\n\t\t\tif (vnode.type._forwarded) {\n\t\t\t\tvalues = assign({}, values);\n\t\t\t\tdelete values.ref;\n\t\t\t}\n\n\t\t\tcheckPropTypes(\n\t\t\t\tvnode.type.propTypes,\n\t\t\t\tvalues,\n\t\t\t\t'prop',\n\t\t\t\tgetDisplayName(vnode),\n\t\t\t\t() => getOwnerStack(vnode)\n\t\t\t);\n\t\t}\n\n\t\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n\t};\n\n\tlet renderCount = 0;\n\tlet currentComponent;\n\toptions._render = vnode => {\n\t\tif (oldRender) {\n\t\t\toldRender(vnode);\n\t\t}\n\t\thooksAllowed = true;\n\n\t\tconst nextComponent = vnode._component;\n\t\tif (nextComponent === currentComponent) {\n\t\t\trenderCount++;\n\t\t} else {\n\t\t\trenderCount = 1;\n\t\t}\n\n\t\tif (renderCount >= 25) {\n\t\t\tthrow new Error(\n\t\t\t\t`Too many re-renders. This is limited to prevent an infinite loop ` +\n\t\t\t\t\t`which may lock up your browser. The component causing this is: ${getDisplayName(\n\t\t\t\t\t\tvnode\n\t\t\t\t\t)}`\n\t\t\t);\n\t\t}\n\n\t\tcurrentComponent = nextComponent;\n\t};\n\n\toptions._hook = (comp, index, type) => {\n\t\tif (!comp || !hooksAllowed) {\n\t\t\tthrow new Error('Hook can only be invoked from render methods.');\n\t\t}\n\n\t\tif (oldHook) oldHook(comp, index, type);\n\t};\n\n\t// Ideally we'd want to print a warning once per component, but we\n\t// don't have access to the vnode that triggered it here. As a\n\t// compromise and to avoid flooding the console with warnings we\n\t// print each deprecation warning only once.\n\tconst warn = (property, message) => ({\n\t\tget() {\n\t\t\tconst key = 'get' + property + message;\n\t\t\tif (deprecations && deprecations.indexOf(key) < 0) {\n\t\t\t\tdeprecations.push(key);\n\t\t\t\tconsole.warn(`getting vnode.${property} is deprecated, ${message}`);\n\t\t\t}\n\t\t},\n\t\tset() {\n\t\t\tconst key = 'set' + property + message;\n\t\t\tif (deprecations && deprecations.indexOf(key) < 0) {\n\t\t\t\tdeprecations.push(key);\n\t\t\t\tconsole.warn(`setting vnode.${property} is not allowed, ${message}`);\n\t\t\t}\n\t\t}\n\t});\n\n\tconst deprecatedAttributes = {\n\t\tnodeName: warn('nodeName', 'use vnode.type'),\n\t\tattributes: warn('attributes', 'use vnode.props'),\n\t\tchildren: warn('children', 'use vnode.props.children')\n\t};\n\n\tconst deprecatedProto = Object.create({}, deprecatedAttributes);\n\n\toptions.vnode = vnode => {\n\t\tconst props = vnode.props;\n\t\tif (\n\t\t\tvnode.type !== null &&\n\t\t\tprops != null &&\n\t\t\t('__source' in props || '__self' in props)\n\t\t) {\n\t\t\tconst newProps = (vnode.props = {});\n\t\t\tfor (let i in props) {\n\t\t\t\tconst v = props[i];\n\t\t\t\tif (i === '__source') vnode.__source = v;\n\t\t\t\telse if (i === '__self') vnode.__self = v;\n\t\t\t\telse newProps[i] = v;\n\t\t\t}\n\t\t}\n\n\t\t// eslint-disable-next-line\n\t\tvnode.__proto__ = deprecatedProto;\n\t\tif (oldVnode) oldVnode(vnode);\n\t};\n\n\toptions.diffed = vnode => {\n\t\tconst { type, _parent: parent } = vnode;\n\t\t// Check if the user passed plain objects as children. Note that we cannot\n\t\t// move this check into `options.vnode` because components can receive\n\t\t// children in any shape they want (e.g.\n\t\t// `<MyJSONFormatter>{{ foo: 123, bar: \"abc\" }}</MyJSONFormatter>`).\n\t\t// Putting this check in `options.diffed` ensures that\n\t\t// `vnode._children` is set and that we only validate the children\n\t\t// that were actually rendered.\n\t\tif (vnode._children) {\n\t\t\tvnode._children.forEach(child => {\n\t\t\t\tif (typeof child === 'object' && child && child.type === undefined) {\n\t\t\t\t\tconst keys = Object.keys(child).join(',');\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Objects are not valid as a child. Encountered an object with the keys {${keys}}.` +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tif (vnode._component === currentComponent) {\n\t\t\trenderCount = 0;\n\t\t}\n\n\t\tif (\n\t\t\ttypeof type === 'string' &&\n\t\t\t(isTableElement(type) ||\n\t\t\t\ttype === 'p' ||\n\t\t\t\ttype === 'a' ||\n\t\t\t\ttype === 'button')\n\t\t) {\n\t\t\t// Avoid false positives when Preact only partially rendered the\n\t\t\t// HTML tree. Whilst we attempt to include the outer DOM in our\n\t\t\t// validation, this wouldn't work on the server for\n\t\t\t// `preact-render-to-string`. There we'd otherwise flood the terminal\n\t\t\t// with false positives, which we'd like to avoid.\n\t\t\tlet domParentName = getClosestDomNodeParentName(parent);\n\t\t\tif (domParentName !== '' && isTableElement(type)) {\n\t\t\t\tif (\n\t\t\t\t\ttype === 'table' &&\n\t\t\t\t\t// Tables can be nested inside each other if it's inside a cell.\n\t\t\t\t\t// See https://developer.mozilla.org/en-US/docs/Learn/HTML/Tables/Advanced#nesting_tables\n\t\t\t\t\tdomParentName !== 'td' &&\n\t\t\t\t\tisTableElement(domParentName)\n\t\t\t\t) {\n\t\t\t\t\tconsole.log(domParentName, parent._dom);\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of table. Your <table> should not have a table-node parent.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t} else if (\n\t\t\t\t\t(type === 'thead' || type === 'tfoot' || type === 'tbody') &&\n\t\t\t\t\tdomParentName !== 'table'\n\t\t\t\t) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t} else if (\n\t\t\t\t\ttype === 'tr' &&\n\t\t\t\t\tdomParentName !== 'thead' &&\n\t\t\t\t\tdomParentName !== 'tfoot' &&\n\t\t\t\t\tdomParentName !== 'tbody'\n\t\t\t\t) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot> parent.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t} else if (type === 'td' && domParentName !== 'tr') {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of table. Your <td> should have a <tr> parent.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t} else if (type === 'th' && domParentName !== 'tr') {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of table. Your <th> should have a <tr>.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} else if (type === 'p') {\n\t\t\t\tlet illegalDomChildrenTypes = getDomChildren(vnode).filter(childType =>\n\t\t\t\t\tILLEGAL_PARAGRAPH_CHILD_ELEMENTS.test(childType)\n\t\t\t\t);\n\t\t\t\tif (illegalDomChildrenTypes.length) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Improper nesting of paragraph. Your <p> should not have ' +\n\t\t\t\t\t\t\tillegalDomChildrenTypes.join(', ') +\n\t\t\t\t\t\t\t'as child-elements.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} else if (type === 'a' || type === 'button') {\n\t\t\t\tif (getDomChildren(vnode).indexOf(type) !== -1) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t`Improper nesting of interactive content. Your <${type}>` +\n\t\t\t\t\t\t\t` should not have other ${type === 'a' ? 'anchor' : 'button'}` +\n\t\t\t\t\t\t\t' tags as child-elements.' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\thooksAllowed = false;\n\n\t\tif (oldDiffed) oldDiffed(vnode);\n\n\t\tif (vnode._children != null) {\n\t\t\tconst keys = [];\n\t\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\t\tconst child = vnode._children[i];\n\t\t\t\tif (!child || child.key == null) continue;\n\n\t\t\t\tconst key = child.key;\n\t\t\t\tif (keys.indexOf(key) !== -1) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Following component has two or more children with the ' +\n\t\t\t\t\t\t\t`same key attribute: \"${key}\". This may cause glitches and misbehavior ` +\n\t\t\t\t\t\t\t'in rendering process. Component: \\n\\n' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\n\t\t\t\t\t// Break early to not spam the console\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tkeys.push(key);\n\t\t\t}\n\t\t}\n\n\t\tif (vnode._component != null && vnode._component.__hooks != null) {\n\t\t\t// Validate that none of the hooks in this component contain arguments that are NaN.\n\t\t\t// This is a common mistake that can be hard to debug, so we want to catch it early.\n\t\t\tconst hooks = vnode._component.__hooks._list;\n\t\t\tif (hooks) {\n\t\t\t\tfor (let i = 0; i < hooks.length; i += 1) {\n\t\t\t\t\tconst hook = hooks[i];\n\t\t\t\t\tif (hook._args) {\n\t\t\t\t\t\tfor (let j = 0; j < hook._args.length; j++) {\n\t\t\t\t\t\t\tconst arg = hook._args[j];\n\t\t\t\t\t\t\tif (isNaN(arg)) {\n\t\t\t\t\t\t\t\tconst componentName = getDisplayName(vnode);\n\t\t\t\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t\t\t\t`Invalid argument passed to hook. Hooks should not be called with NaN in the dependency array. Hook index ${i} in component ${componentName} was called with NaN.`\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n}\n\nconst setState = Component.prototype.setState;\nComponent.prototype.setState = function (update, callback) {\n\tif (this._vnode == null) {\n\t\t// `this._vnode` will be `null` during componentWillMount. But it\n\t\t// is perfectly valid to call `setState` during cWM. So we\n\t\t// need an additional check to verify that we are dealing with a\n\t\t// call inside constructor.\n\t\tif (this.state == null) {\n\t\t\tconsole.warn(\n\t\t\t\t`Calling \"this.setState\" inside the constructor of a component is a ` +\n\t\t\t\t\t`no-op and might be a bug in your application. Instead, set ` +\n\t\t\t\t\t`\"this.state = {}\" directly.\\n\\n${getOwnerStack(getCurrentVNode())}`\n\t\t\t);\n\t\t}\n\t}\n\n\treturn setState.call(this, update, callback);\n};\n\nfunction isTableElement(type) {\n\treturn (\n\t\ttype === 'table' ||\n\t\ttype === 'tfoot' ||\n\t\ttype === 'tbody' ||\n\t\ttype === 'thead' ||\n\t\ttype === 'td' ||\n\t\ttype === 'tr' ||\n\t\ttype === 'th'\n\t);\n}\n\nconst ILLEGAL_PARAGRAPH_CHILD_ELEMENTS =\n\t/^(address|article|aside|blockquote|details|div|dl|fieldset|figcaption|figure|footer|form|h1|h2|h3|h4|h5|h6|header|hgroup|hr|main|menu|nav|ol|p|pre|search|section|table|ul)$/;\n\nconst forceUpdate = Component.prototype.forceUpdate;\nComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode == null) {\n\t\tconsole.warn(\n\t\t\t`Calling \"this.forceUpdate\" inside the constructor of a component is a ` +\n\t\t\t\t`no-op and might be a bug in your application.\\n\\n${getOwnerStack(\n\t\t\t\t\tgetCurrentVNode()\n\t\t\t\t)}`\n\t\t);\n\t} else if (this._parentDom == null) {\n\t\tconsole.warn(\n\t\t\t`Can't call \"this.forceUpdate\" on an unmounted component. This is a no-op, ` +\n\t\t\t\t`but it indicates a memory leak in your application. To fix, cancel all ` +\n\t\t\t\t`subscriptions and asynchronous tasks in the componentWillUnmount method.` +\n\t\t\t\t`\\n\\n${getOwnerStack(this._vnode)}`\n\t\t);\n\t}\n\treturn forceUpdate.call(this, callback);\n};\n\n/**\n * Serialize a vnode tree to a string\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function serializeVNode(vnode) {\n\tlet { props } = vnode;\n\tlet name = getDisplayName(vnode);\n\n\tlet attrs = '';\n\tfor (let prop in props) {\n\t\tif (props.hasOwnProperty(prop) && prop !== 'children') {\n\t\t\tlet value = props[prop];\n\n\t\t\t// If it is an object but doesn't have toString(), use Object.toString\n\t\t\tif (typeof value == 'function') {\n\t\t\t\tvalue = `function ${value.displayName || value.name}() {}`;\n\t\t\t}\n\n\t\t\tvalue =\n\t\t\t\tObject(value) === value && !value.toString\n\t\t\t\t\t? Object.prototype.toString.call(value)\n\t\t\t\t\t: value + '';\n\n\t\t\tattrs += ` ${prop}=${JSON.stringify(value)}`;\n\t\t}\n\t}\n\n\tlet children = props.children;\n\treturn `<${name}${attrs}${\n\t\tchildren && children.length ? '>..</' + name + '>' : ' />'\n\t}`;\n}\n\noptions._hydrationMismatch = (newVNode, excessDomChildren) => {\n\tconst { type } = newVNode;\n\tconst availableTypes = excessDomChildren\n\t\t.map(child => child && child.localName)\n\t\t.filter(Boolean);\n\tconsole.error(\n\t\t`Expected a DOM node of type ${type} but found ${availableTypes.join(', ')}as available DOM-node(s), this is caused by the SSR'd HTML containing different DOM-nodes compared to the hydrated one.\\n\\n${getOwnerStack(newVNode)}`\n\t);\n};\n", "export const ELEMENT_NODE = 1;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\n", "/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\nexport function isNaN(value) {\n\treturn value !== value;\n}\n", "import { initDebug } from './debug';\nimport 'preact/devtools';\n\ninitDebug();\n\nexport { resetPropWarnings } from './check-props';\n"], "names": ["loggedTypeFailures", "getDisplayName", "vnode", "type", "Fragment", "displayName", "name", "renderStack", "ownerStack", "getCurrentVNode", "length", "showJsxSourcePluginWarning", "isPossibleOwner", "getOwnerStack", "stack", "next", "__o", "push", "reduce", "acc", "owner", "source", "__source", "fileName", "lineNumber", "console", "warn", "isWeakMapSupported", "WeakMap", "getDomChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__k", "for<PERSON>ach", "child", "apply", "getClosestDomNodeParentName", "parent", "__", "__e", "parentNode", "localName", "setState", "Component", "prototype", "isTableElement", "update", "callback", "this", "__v", "state", "call", "ILLEGAL_PARAGRAPH_CHILD_ELEMENTS", "forceUpdate", "serializeVNode", "props", "attrs", "prop", "hasOwnProperty", "value", "Object", "toString", "JSON", "stringify", "children", "__P", "options", "__m", "newVNode", "excessDomChildren", "availableTypes", "map", "filter", "Boolean", "error", "join", "oldDiff", "__b", "oldDiffed", "diffed", "oldRoot", "oldVNode", "old<PERSON><PERSON>", "__r", "pop", "setupComponentStack", "hooksAllowed", "oldBeforeDiff", "oldVnode", "oldCatchError", "oldHook", "__h", "warnedComponents", "useEffect", "useLayoutEffect", "lazyPropTypes", "deprecations", "errorInfo", "__c", "then", "promise", "Error", "componentStack", "setTimeout", "e", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "componentName", "undefined", "Array", "isArray", "ref", "key", "propTypes", "has", "m", "lazyVNode", "set", "values", "__f", "obj", "i", "assign", "typeSpecs", "location", "getStack", "keys", "typeSpecName", "message", "checkPropTypes", "currentComponent", "renderCount", "nextComponent", "comp", "index", "property", "get", "indexOf", "deprecatedAttributes", "nodeName", "attributes", "deprecatedProto", "create", "newProps", "v", "__self", "__proto__", "domParentName", "log", "illegalDomChildrenTypes", "childType", "test", "__H", "hooks", "hook", "j", "initDebug"], "mappings": "wTAAA,IAEIA,EAAqB,CAAA,ECMlB,SAASC,EAAeC,GAC9B,OAAIA,EAAMC,OAASC,EAAAA,SACX,WACwB,mBAAdF,EAAMC,KAChBD,EAAMC,KAAKE,aAAeH,EAAMC,KAAKG,KACb,iBAAdJ,EAAMC,KAChBD,EAAMC,KAGP,OACR,CAMA,IAAII,EAAc,GAoBdC,EAAa,YAMDC,IACf,OAAOF,EAAYG,OAAS,EAAIH,EAAYA,EAAYG,OAAS,GAAK,IACvE,CAQA,IAAIC,GAA6B,EAMjC,SAASC,EAAgBV,GACxB,MAA4B,mBAAdA,EAAMC,MAAsBD,EAAMC,MAAQC,EAAAA,QACzD,CAOO,SAASS,EAAcX,GAG7B,IAFA,IAAMY,EAAQ,CAACZ,GACXa,EAAOb,EACW,MAAfa,EAAIC,KACVF,EAAMG,KAAKF,EAAIC,KACfD,EAAOA,EAAIC,IAGZ,OAAOF,EAAMI,OAAO,SAACC,EAAKC,GACzBD,GAAelB,QAAAA,EAAemB,GAE9B,IAAMC,EAASD,EAAME,SAUrB,OATID,EACHF,GAAeE,QAAAA,EAAOE,SAAYF,IAAAA,EAAOG,WAC1C,IAAWb,GACVc,QAAQC,KACP,kLAGFf,GAA6B,EAErBQ,EAAO,IAChB,EAAG,GACJ,CCnFA,IAAMQ,EAAuC,mBAAXC,QAMlC,SAASC,EAAe3B,GACvB,IAAI4B,EAAc,GAElB,OAAK5B,EAAK6B,KAEV7B,EAAK6B,IAAWC,QAAQ,SAAAC,GACnBA,GAA+B,mBAAfA,EAAM9B,KACzB2B,EAAYb,KAAKiB,MAAMJ,EAAaD,EAAeI,IACzCA,GAA+B,iBAAfA,EAAM9B,MAChC2B,EAAYb,KAAKgB,EAAM9B,KAEzB,GAEO2B,GAVsBA,CAW9B,CAMA,SAASK,EAA4BC,GACpC,OAAKA,EACqB,mBAAfA,EAAOjC,KACK,MAAlBiC,EAAMC,GACU,MAAfD,EAAME,KAA2C,MAA1BF,EAAME,IAAMC,WAC/BH,EAAME,IAAMC,WAAWC,UAExB,GAEDL,EAA4BC,EAAMC,IAEZD,EAAOjC,KAVjB,EAWrB,CA4bA,IAAMsC,EAAWC,YAAUC,UAAUF,SAmBrC,SAASG,EAAezC,GACvB,MACU,UAATA,GACS,UAATA,GACS,UAATA,GACS,UAATA,GACS,OAATA,GACS,OAATA,GACS,OAATA,CAEF,CA5BAuC,EAASA,UAACC,UAAUF,SAAW,SAAUI,EAAQC,GAehD,OAdmB,MAAfC,KAAIC,KAKW,MAAdD,KAAKE,OACRxB,QAAQC,KACP,gKAEmCb,EAAcJ,MAK7CgC,EAASS,KAAKH,KAAMF,EAAQC,EACpC,EAcA,IAAMK,EACL,+KAEKC,EAAcV,EAASA,UAACC,UAAUS,YAyBxB,SAAAC,EAAenD,GAC9B,IAAMoD,EAAUpD,EAAVoD,MACFhD,EAAOL,EAAeC,GAEtBqD,EAAQ,GACZ,IAAK,IAAIC,KAAQF,EAChB,GAAIA,EAAMG,eAAeD,IAAkB,aAATA,EAAqB,CACtD,IAAIE,EAAQJ,EAAME,GAGE,mBAATE,IACVA,eAAoBA,EAAMrD,aAAeqD,EAAMpD,MAChD,SAEAoD,EACCC,OAAOD,KAAWA,GAAUA,EAAME,SAE/BF,EAAQ,GADRC,OAAOhB,UAAUiB,SAASV,KAAKQ,GAGnCH,GAAaC,IAAAA,EAAQK,IAAAA,KAAKC,UAAUJ,EACrC,CAGD,IAAIK,EAAWT,EAAMS,SACrB,UAAWzD,EAAOiD,GACjBQ,GAAYA,EAASrD,OAAS,QAAUJ,EAAO,IAAM,MAEvD,CAnDAoC,EAAAA,UAAUC,UAAUS,YAAc,SAAUN,GAgB3C,OAfmB,MAAfC,KAAIC,IACPvB,QAAQC,KACP,0HACqDb,EACnDJ,MAG0B,MAAnBsC,KAAIiB,KACdvC,QAAQC,KACP,iOAGQb,EAAckC,KAAIC,MAGrBI,EAAYF,KAAKH,KAAMD,EAC/B,EAoCAmB,EAAOA,QAAAC,IAAsB,SAACC,EAAUC,GACvC,IAAQjE,EAASgE,EAAThE,KACFkE,EAAiBD,EACrBE,IAAI,SAAArC,GAAK,OAAIA,GAASA,EAAMO,SAAS,GACrC+B,OAAOC,SACT/C,QAAQgD,MACwBtE,+BAAAA,EAAkBkE,cAAAA,EAAeK,KAAK,oIAAmI7D,EAAcsD,GAExN,EA1hBO,YDkDA,WACN,IAAIQ,EAAUV,EAAAA,QAAOW,IACjBC,EAAYZ,EAAOA,QAACa,OACpBC,EAAUd,EAAOA,QAAA5B,GACjB2C,EAAWf,EAAOA,QAAC/D,MACnB+E,EAAYhB,EAAOA,QAAAiB,IAEvBjB,EAAAA,QAAQa,OAAS,SAAA5E,GACZU,EAAgBV,IACnBM,EAAW2E,MAEZ5E,EAAY4E,MACRN,GAAWA,EAAU3E,EAC1B,EAEA+D,EAAOA,QAAAW,IAAS,SAAA1E,GACXU,EAAgBV,IACnBK,EAAYU,KAAKf,GAEdyE,GAASA,EAAQzE,EACtB,EAEA+D,UAAO5B,GAAS,SAACnC,EAAOkC,GACvB5B,EAAa,GACTuE,GAASA,EAAQ7E,EAAOkC,EAC7B,EAEA6B,EAAAA,QAAQ/D,MAAQ,SAAAA,GACfA,EAAKc,IACJR,EAAWE,OAAS,EAAIF,EAAWA,EAAWE,OAAS,GAAK,KACzDsE,GAAUA,EAAS9E,EACxB,EAEA+D,EAAOA,QAAAiB,IAAW,SAAAhF,GACbU,EAAgBV,IACnBM,EAAWS,KAAKf,GAGb+E,GAAWA,EAAU/E,EAC1B,CACD,CCzFCkF,GAEA,IAAIC,GAAe,EAGfC,EAAgBrB,EAAAA,QAAOW,IACvBC,EAAYZ,EAAOA,QAACa,OACpBS,EAAWtB,EAAOA,QAAC/D,MACnB+E,EAAYhB,EAAOA,QAAAiB,IACnBM,EAAgBvB,EAAOA,QAAA3B,IACvByC,EAAUd,UAAO5B,GACjBoD,EAAUxB,EAAAA,QAAOyB,IACfC,EAAoBhE,EAEvB,CACAiE,UAAW,IAAIhE,QACfiE,gBAAiB,IAAIjE,QACrBkE,cAAe,IAAIlE,SAJnB,KAMGmE,EAAe,GAErB9B,EAAOA,QAAA3B,IAAe,SAACmC,EAAOvE,EAAO8E,EAAUgB,GAE9C,GADgB9F,GAASA,EAAK+F,KACQ,mBAAdxB,EAAMyB,KAAoB,CACjD,IAAMC,EAAU1B,EAChBA,EAAQ,IAAI2B,MAAK,iDACiCnG,EAAeC,IAIjE,IADA,IAAIkC,EAASlC,EACNkC,EAAQA,EAASA,EAAMC,GAC7B,GAAID,EAAM6D,KAAe7D,EAAM6D,IAAAA,IAA8B,CAC5DxB,EAAQ0B,EACR,KACD,CAKD,GAAI1B,aAAiB2B,MACpB,MAAM3B,CAER,CAEA,KACCuB,EAAYA,GAAa,CAAA,GACfK,eAAiBxF,EAAcX,GACzCsF,EAAcf,EAAOvE,EAAO8E,EAAUgB,GAKb,mBAAdvB,EAAMyB,MAChBI,WAAW,WACV,MAAM7B,CACP,EAIF,CAFE,MAAO8B,GACR,MAAMA,CACP,CACD,EAEAtC,EAAOA,QAAA5B,GAAS,SAACnC,EAAOqC,GACvB,IAAKA,EACJ,UAAU6D,MACT,uIAKF,IAAII,EACJ,OAAQjE,EAAWkE,UAClB,KChIyB,EDiIzB,KC/HmC,GDgInC,KCjI0B,EDkIzBD,GAAU,EACV,MACD,QACCA,GAAU,EAGZ,IAAKA,EAAS,CACb,IAAIE,EAAgBzG,EAAeC,GACnC,MAAM,IAAIkG,8EAC8D7D,EAAU,qBAAqBmE,EAAa,QAAQnE,EAC5H,KACD,CAEIwC,GAASA,EAAQ7E,EAAOqC,EAC7B,EAEA0B,EAAOA,QAAAW,IAAS,SAAA1E,GACf,IAAMC,EAASD,EAATC,KAIN,GAFAkF,GAAe,OAEFsB,IAATxG,EACH,MAAM,IAAIiG,MACT,+IAEC/C,EAAenD,GAAM,OACdW,EAAcX,IAEbC,GAAQ,MAARA,GAA+B,iBAARA,EAAkB,CACnD,QAAuBwG,IAAnBxG,EAAI4B,UAA0C4E,IAAdxG,EAAImC,IACvC,MAAU,IAAA8D,MACT,2CAA2CjG,EAA3C,wEAEYF,EAAeC,GAAM,MAAMmD,EAAelD,GAFtD,uBAGqBF,EAAeC,GAHpC,wFAKQW,EAAcX,IAIxB,MAAU,IAAAkG,MACT,4CACEQ,MAAMC,QAAQ1G,GAAQ,QAAUA,GAEpC,CAEA,QACewG,IAAdzG,EAAM4G,KACc,mBAAb5G,EAAM4G,KACO,iBAAb5G,EAAM4G,OACX,aAAc5G,GAEhB,UAAUkG,MACT,0GACoClG,EAAM4G,IAAgB,cACzDzD,EAAenD,GAAM,OACdW,EAAcX,IAIxB,GAAyB,iBAAdA,EAAMC,KAChB,IAAK,IAAM4G,KAAO7G,EAAMoD,MACvB,GACY,MAAXyD,EAAI,IACO,MAAXA,EAAI,IACuB,mBAApB7G,EAAMoD,MAAMyD,IACC,MAApB7G,EAAMoD,MAAMyD,GAEZ,MAAM,IAAIX,MACT,iBAAgBW,EAAhB,oDACoB7G,EAAMoD,MAAMyD,iBAC/B1D,EAAenD,GACRW,OAAAA,EAAcX,IAO1B,GAAyB,mBAAdA,EAAMC,MAAsBD,EAAMC,KAAK6G,UAAW,CAC5D,GAC4B,SAA3B9G,EAAMC,KAAKE,aACXsF,IACCA,EAAiBG,cAAcmB,IAAI/G,EAAMC,MACzC,CACD,IAAM+G,EACL,yFACD,IACC,IAAMC,EAAYjH,EAAMC,OACxBwF,EAAiBG,cAAcsB,IAAIlH,EAAMC,MAAM,GAC/CsB,QAAQC,KACPwF,EAAsCjH,kCAAAA,EAAekH,GAMvD,CAJE,MAAOhB,GACR1E,QAAQC,KACPwF,EAAI,8DAEN,CACD,CAEA,IAAIG,EAASnH,EAAMoD,MACfpD,EAAMC,KAAImH,YACbD,WElOmBE,EAAKjE,GAC3B,IAAK,IAAIkE,KAAKlE,EAAOiE,EAAIC,GAAKlE,EAAMkE,GACpC,OAA6BD,CAC9B,CF+NaE,CAAO,CAAA,EAAIJ,IACNP,IFnNX,SACNY,EACAL,EACAM,EACAjB,EACAkB,GAEAjE,OAAOkE,KAAKH,GAAW1F,QAAQ,SAAA8F,GAC9B,IAAIrD,EACJ,IACCA,EAAQiD,EAAUI,GACjBT,EACAS,EACApB,EE4MA,OF1MA,KAtCyB,+CA2C3B,CAFE,MAAOH,GACR9B,EAAQ8B,CACT,CACI9B,KAAWA,EAAMsD,WAAW/H,KAC/BA,EAAmByE,EAAMsD,UAAW,EACpCtG,QAAQgD,2BACqBA,EAAMsD,SAChCH,GAAQ,KAASA,KAAiB,KAIvC,EACD,CEwLGI,CACC9H,EAAMC,KAAK6G,UACXK,EACA,EACApH,EAAeC,GACf,WAAM,OAAAW,EAAcX,EAAM,EAE5B,CAEIoF,GAAeA,EAAcpF,EAClC,EAEA,IACI+H,EADAC,EAAc,EAElBjE,EAAOA,QAAAiB,IAAW,SAAAhF,GACb+E,GACHA,EAAU/E,GAEXmF,GAAe,EAEf,IAAM8C,EAAgBjI,EAAK+F,IAO3B,GANIkC,IAAkBF,EACrBC,IAEAA,EAAc,EAGXA,GAAe,GAClB,MAAM,IAAI9B,MACT,mIACmEnG,EACjEC,IAKJ+H,EAAmBE,CACpB,EAEAlE,UAAOyB,IAAS,SAAC0C,EAAMC,EAAOlI,GAC7B,IAAKiI,IAAS/C,EACb,MAAU,IAAAe,MAAM,iDAGbX,GAASA,EAAQ2C,EAAMC,EAAOlI,EACnC,EAMA,IAAMuB,EAAO,SAAC4G,EAAUP,GAAa,MAAA,CACpCQ,IAAGA,WACF,IAAMxB,EAAM,MAAQuB,EAAWP,EAC3BhC,GAAgBA,EAAayC,QAAQzB,GAAO,IAC/ChB,EAAa9E,KAAK8F,GAClBtF,QAAQC,KAAI,iBAAkB4G,EAA2BP,mBAAAA,GAE3D,EACAX,IAAGA,WACF,IAAML,EAAM,MAAQuB,EAAWP,EAC3BhC,GAAgBA,EAAayC,QAAQzB,GAAO,IAC/ChB,EAAa9E,KAAK8F,GAClBtF,QAAQC,sBAAsB4G,EAAQ,oBAAoBP,GAE5D,EACA,EAEKU,EAAuB,CAC5BC,SAAUhH,EAAK,WAAY,kBAC3BiH,WAAYjH,EAAK,aAAc,mBAC/BqC,SAAUrC,EAAK,WAAY,6BAGtBkH,EAAkBjF,OAAOkF,OAAO,CAAE,EAAEJ,GAE1CxE,EAAOA,QAAC/D,MAAQ,SAAAA,GACf,IAAMoD,EAAQpD,EAAMoD,MACpB,GACgB,OAAfpD,EAAMC,MACG,MAATmD,IACC,aAAcA,GAAS,WAAYA,GACnC,CACD,IAAMwF,EAAY5I,EAAMoD,MAAQ,CAAA,EAChC,IAAK,IAAIkE,KAAKlE,EAAO,CACpB,IAAMyF,EAAIzF,EAAMkE,GACN,aAANA,EAAkBtH,EAAMoB,SAAWyH,EACxB,WAANvB,EAAgBtH,EAAM8I,OAASD,EACnCD,EAAStB,GAAKuB,CACpB,CACD,CAGA7I,EAAM+I,UAAYL,EACdrD,GAAUA,EAASrF,EACxB,EAEA+D,EAAAA,QAAQa,OAAS,SAAA5E,GAChB,IEnUoBwD,EFmUZvD,EAA0BD,EAA1BC,KAAeiC,EAAWlC,EAAKmC,GAwBvC,GAhBInC,EAAK6B,KACR7B,EAAK6B,IAAWC,QAAQ,SAAAC,GACvB,GAAqB,iBAAVA,GAAsBA,QAAwB0E,IAAf1E,EAAM9B,KAAoB,CACnE,IAAM0H,EAAOlE,OAAOkE,KAAK5F,GAAOyC,KAAK,KACrC,UAAU0B,MACT,0EAA0EyB,EAA1E,SACQhH,EAAcX,GAExB,CACD,GAGGA,EAAK+F,MAAgBgC,IACxBC,EAAc,GAIE,iBAAT/H,IACNyC,EAAezC,IACN,MAATA,GACS,MAATA,GACS,WAATA,GACA,CAMD,IAAI+I,EAAgB/G,EAA4BC,GAChD,GAAsB,KAAlB8G,GAAwBtG,EAAezC,GAEhC,UAATA,GAGkB,OAAlB+I,GACAtG,EAAesG,IAEfzH,QAAQ0H,IAAID,EAAe9G,EAAME,KACjCb,QAAQgD,MACP,+EACCpB,EAAenD,GACRW,OAAAA,EAAcX,KAGb,UAATC,GAA6B,UAATA,GAA6B,UAATA,GACvB,UAAlB+I,EAQS,OAAT/I,GACkB,UAAlB+I,GACkB,UAAlBA,GACkB,UAAlBA,EAEAzH,QAAQgD,MACP,iFACCpB,EAAenD,UACRW,EAAcX,IAEJ,OAATC,GAAmC,OAAlB+I,EAC3BzH,QAAQgD,MACP,kEACCpB,EAAenD,UACRW,EAAcX,IAEJ,OAATC,GAAmC,OAAlB+I,GAC3BzH,QAAQgD,MACP,2DACCpB,EAAenD,GAAM,OACdW,EAAcX,IA1BvBuB,QAAQgD,MACP,oFACCpB,EAAenD,GAAM,OACdW,EAAcX,YA0BL,MAATC,EAAc,CACxB,IAAIiJ,EAA0BvH,EAAe3B,GAAOqE,OAAO,SAAA8E,GAAS,OACnElG,EAAiCmG,KAAKD,EAAU,GAE7CD,EAAwB1I,QAC3Be,QAAQgD,MACP,2DACC2E,EAAwB1E,KAAK,MAC7B,qBACArB,EAAenD,GACRW,OAAAA,EAAcX,GAGzB,KAAoB,MAATC,GAAyB,WAATA,IACmB,IAAzC0B,EAAe3B,GAAOsI,QAAQrI,IACjCsB,QAAQgD,MACP,kDAAkDtE,EAAlD,4BACoC,MAATA,EAAe,SAAW,UACpD,2BACAkD,EAAenD,GAAM,OACdW,EAAcX,GAI1B,CAMA,GAJAmF,GAAe,EAEXR,GAAWA,EAAU3E,GAEF,MAAnBA,EAAK6B,IAER,IADA,IAAM8F,EAAO,GACJL,EAAI,EAAGA,EAAItH,EAAK6B,IAAWrB,OAAQ8G,IAAK,CAChD,IAAMvF,EAAQ/B,EAAK6B,IAAWyF,GAC9B,GAAKvF,GAAsB,MAAbA,EAAM8E,IAApB,CAEA,IAAMA,EAAM9E,EAAM8E,IAClB,IAA2B,IAAvBc,EAAKW,QAAQzB,GAAa,CAC7BtF,QAAQgD,MACP,8EACyBsC,EADzB,mFAGC1D,EAAenD,GACRW,OAAAA,EAAcX,IAIvB,KACD,CAEA2H,EAAK5G,KAAK8F,EAdV,CAeD,CAGD,GAAwB,MAApB7G,EAAK+F,KAAmD,MAA5B/F,EAAK+F,IAAAsD,IAA6B,CAGjE,IAAMC,EAAQtJ,EAAK+F,IAAAsD,IAAAlH,GACnB,GAAImH,EACH,IAAK,IAAIhC,EAAI,EAAGA,EAAIgC,EAAM9I,OAAQ8G,GAAK,EAAG,CACzC,IAAMiC,EAAOD,EAAMhC,GACnB,GAAIiC,EAAIF,IACP,IAAK,IAAIG,EAAI,EAAGA,EAAID,EAAIF,IAAO7I,OAAQgJ,IAEtC,IEvdehG,EFsdH+F,EAAIF,IAAOG,KErdZhG,EFsdK,CACf,IAAMgD,EAAgBzG,EAAeC,GACrCuB,QAAQC,KAAI,4GACiG8F,EAAkBd,iBAAAA,EAC/H,wBACD,CAGH,CAEF,CACD,CACD,CG5eAiD,uBLIO,WACN3J,EAAqB,CAAA,CACtB"}