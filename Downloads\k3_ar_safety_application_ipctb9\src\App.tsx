import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { ARScanner } from "./components/ARScanner";
import { Dashboard } from "./components/Dashboard";
import { ReportsPanel } from "./components/ReportsPanel";
import { HazardManagement } from "./components/HazardManagement";
import { useState } from "react";

export default function App() {
  const [activeTab, setActiveTab] = useState<"ar" | "dashboard" | "reports" | "hazards">("ar");

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <header className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm border-b shadow-sm">
        <div className="flex justify-between items-center px-4 h-16">
          <h2 className="text-xl font-semibold text-blue-600">K3 AR Safety</h2>
          <SignOutButton />
        </div>
        <Authenticated>
          <nav className="flex border-t">
            <button
              onClick={() => setActiveTab("ar")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "ar"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              AR Scanner
            </button>
            <button
              onClick={() => setActiveTab("dashboard")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "dashboard"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab("reports")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "reports"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Reports
            </button>
            <button
              onClick={() => setActiveTab("hazards")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "hazards"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Hazards
            </button>
          </nav>
        </Authenticated>
      </header>

      <main className="flex-1">
        <Content activeTab={activeTab} />
      </main>
      <Toaster />
    </div>
  );
}

function Content({ activeTab }: { activeTab: string }) {
  const loggedInUser = useQuery(api.auth.loggedInUser);

  if (loggedInUser === undefined) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <Authenticated>
        {activeTab === "ar" && <ARScanner />}
        {activeTab === "dashboard" && <Dashboard />}
        {activeTab === "reports" && <ReportsPanel />}
        {activeTab === "hazards" && <HazardManagement />}
      </Authenticated>

      <Unauthenticated>
        <div className="flex items-center justify-center h-full p-8">
          <div className="w-full max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-blue-600 mb-4">
                K3 AR Safety System
              </h1>
              <p className="text-xl text-gray-600">
                Augmented Reality untuk Keselamatan Kerja
              </p>
              <p className="text-gray-500 mt-2">
                Deteksi bahaya di tempat kerja dengan teknologi AR
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}
