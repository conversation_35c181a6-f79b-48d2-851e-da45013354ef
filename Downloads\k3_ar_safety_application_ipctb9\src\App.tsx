import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { ARScanner } from "./components/ARScanner";
import { Dashboard } from "./components/Dashboard";
import { ReportsPanel } from "./components/ReportsPanel";
import { HazardManagement } from "./components/HazardManagement";
import { LanguageSwitcher } from "./components/LanguageSwitcher";
import { LanguageProvider, useLanguage } from "./contexts/LanguageContext";
import { useState } from "react";

export default function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

function AppContent() {
  const [activeTab, setActiveTab] = useState<"ar" | "dashboard" | "reports" | "hazards">("ar");
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <header className="sticky top-0 z-10 bg-white/80 backdrop-blur-sm border-b shadow-sm">
        <div className="flex justify-between items-center px-4 h-16">
          <h2 className="text-xl font-semibold text-blue-600">{t('app.title')}</h2>
          <div className="flex items-center gap-4">
            <LanguageSwitcher />
            <SignOutButton />
          </div>
        </div>
        <Authenticated>
          <nav className="flex border-t">
            <button
              onClick={() => setActiveTab("ar")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "ar"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t('nav.ar_scanner')}
            </button>
            <button
              onClick={() => setActiveTab("dashboard")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "dashboard"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t('nav.dashboard')}
            </button>
            <button
              onClick={() => setActiveTab("reports")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "reports"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t('nav.reports')}
            </button>
            <button
              onClick={() => setActiveTab("hazards")}
              className={`flex-1 py-3 px-4 text-sm font-medium ${
                activeTab === "hazards"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t('nav.hazards')}
            </button>
          </nav>
        </Authenticated>
      </header>

      <main className="flex-1">
        <Content activeTab={activeTab} />
      </main>
      <Toaster />
    </div>
  );
}

function Content({ activeTab }: { activeTab: string }) {
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const { t } = useLanguage();

  if (loggedInUser === undefined) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <Authenticated>
        {activeTab === "ar" && <ARScanner />}
        {activeTab === "dashboard" && <Dashboard />}
        {activeTab === "reports" && <ReportsPanel />}
        {activeTab === "hazards" && <HazardManagement />}
      </Authenticated>

      <Unauthenticated>
        <div className="flex items-center justify-center h-full p-8">
          <div className="w-full max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-blue-600 mb-4">
                K3 AR Safety System
              </h1>
              <p className="text-xl text-gray-600">
                Augmented Reality untuk Keselamatan Kerja
              </p>
              <p className="text-gray-500 mt-2">
                Deteksi bahaya di tempat kerja dengan teknologi AR
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}
