import { OAuth2Client } from "oslo/oauth2";
import { TimeSpan, createDate } from "oslo";
const authorizeEndpoint = "https://www.reddit.com/api/v1/authorize";
const tokenEndpoint = "https://www.reddit.com/api/v1/access_token";
export class Reddit {
    client;
    clientSecret;
    constructor(clientId, clientSecret, redirectURI) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI
        });
        this.clientSecret = clientSecret;
    }
    async createAuthorizationURL(state, options) {
        return await this.client.createAuthorizationURL({
            state,
            scopes: options?.scopes ?? []
        });
    }
    async validateAuthorizationCode(code) {
        const result = await this.client.validateAuthorizationCode(code, {
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s")),
            refreshToken: result.refresh_token ?? null
        };
        return tokens;
    }
    async refreshAccessToken(refreshToken) {
        const result = await this.client.refreshAccessToken(refreshToken, {
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s")),
            refreshToken: result.refresh_token ?? null
        };
        return tokens;
    }
}
