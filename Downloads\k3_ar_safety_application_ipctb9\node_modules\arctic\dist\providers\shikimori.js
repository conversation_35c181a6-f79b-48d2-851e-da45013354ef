import { OAuth2Client } from "oslo/oauth2";
const authorizeEndpoint = "https://shikimori.one/oauth/authorize";
const tokenEndpoint = "https://shikimori.one/oauth/token";
export class Shikimori {
    client;
    clientSecret;
    constructor(clientId, clientSecret, redirectURI) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI
        });
        this.clientSecret = clientSecret;
    }
    createAuthorizationURL(state, scopes) {
        return this.client.createAuthorizationURL({ state, scopes });
    }
    async validateAuthorizationCode(code) {
        const result = await this.client.validateAuthorizationCode(code, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        return {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: new Date((result.created_at + result.expires_in) * 1000)
        };
    }
    async refreshAccessToken(refreshToken) {
        const result = await this.client.refreshAccessToken(refreshToken, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        return {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: new Date((result.created_at + result.expires_in) * 1000)
        };
    }
}
