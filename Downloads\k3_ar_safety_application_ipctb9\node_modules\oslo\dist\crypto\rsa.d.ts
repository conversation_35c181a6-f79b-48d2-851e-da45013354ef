import type { TypedArray } from "../index.js";
import type { KeyPair } from "./index.js";
import type { SHAHash } from "./sha.js";
export declare class RSASSAPKCS1v1_5 {
    private hash;
    constructor(hash: SHAHash);
    verify(publicKey: <PERSON><PERSON>yBuffer | TypedArray, signature: <PERSON><PERSON><PERSON><PERSON><PERSON>er | TypedArray, data: <PERSON><PERSON><PERSON><PERSON>uffer | TypedArray): Promise<boolean>;
    sign(privateKey: A<PERSON>yBuffer | TypedArray, data: ArrayBuffer | TypedArray): Promise<ArrayBuffer>;
    generateKeyPair(modulusLength?: 2048 | 4096): Promise<KeyPair>;
}
export declare class RSASSAPSS {
    private hash;
    private saltLength;
    constructor(hash: SHAHash);
    verify(publicKey: ArrayBuffer | TypedArray, signature: A<PERSON>y<PERSON>uffer | TypedArray, data: ArrayBuffer | TypedArray): Promise<boolean>;
    sign(privateKey: <PERSON><PERSON><PERSON><PERSON>uffer | TypedArray, data: ArrayBuffer | TypedArray): Promise<ArrayBuffer>;
    generateKeyPair(modulusLength?: 2048 | 4096): Promise<KeyPair>;
}
