import type { TypedArray } from "../index.js";
import type { SHAHash } from "./sha.js";
export declare class HMAC {
    private hash;
    constructor(hash: SHAHash);
    verify(key: ArrayBuffer | TypedArray, signature: <PERSON><PERSON><PERSON><PERSON>uffer | TypedArray, data: A<PERSON>yBuffer | TypedArray): Promise<boolean>;
    sign(key: <PERSON><PERSON><PERSON><PERSON>uffer | TypedArray, data: ArrayBuffer | TypedArray): Promise<ArrayBuffer>;
    generateKey(): Promise<ArrayBuffer>;
}
