import type { TypedArray } from "../index.js";
export interface AttestationResponse {
    clientDataJSON: A<PERSON>y<PERSON><PERSON>er | TypedArray;
    authenticatorData: <PERSON><PERSON>y<PERSON>uffer | TypedArray;
}
export interface AssertionResponse {
    clientDataJSON: <PERSON><PERSON><PERSON><PERSON><PERSON>er | TypedArray;
    authenticatorData: <PERSON><PERSON><PERSON><PERSON><PERSON>er | TypedArray;
    signature: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | TypedArray;
}
export declare class WebAuthnController {
    private originURL;
    constructor(origin: string);
    validateAttestationResponse(response: AttestationResponse, challenge: <PERSON><PERSON>y<PERSON>uffer | TypedArray): Promise<void>;
    validateAssertionResponse(algorithm: "ES256" | "RS256", publicKey: <PERSON><PERSON><PERSON><PERSON><PERSON>er | TypedArray, response: AssertionResponse, challenge: ArrayBuffer | TypedArray): Promise<void>;
    private verifyClientDataJSON;
    private verifyAuthenticatorData;
}
