import { TimeSpan, createDate } from "oslo";
import { OAuth2Client } from "oslo/oauth2";
const authorizeEndpoint = "https://myanimelist.net/v1/oauth2/authorize";
const tokenEndpoint = "https://myanimelist.net/v1/oauth2/token";
export class MyAnimeList {
    client;
    clientSecret;
    constructor(clientId, clientSecret, options) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI: options?.redirectURI
        });
        this.clientSecret = clientSecret;
    }
    async createAuthorizationURL(state, codeVerifier) {
        return await this.client.createAuthorizationURL({
            state,
            codeVerifier,
            codeChallengeMethod: "plain"
        });
    }
    async validateAuthorizationCode(code, codeVerifier) {
        const result = await this.client.validateAuthorizationCode(code, {
            credentials: this.clientSecret,
            codeVerifier
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
    async refreshAccessToken(refreshToken) {
        const result = await this.client.refreshAccessToken(refreshToken, {
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
}
