import { OAuth2Client } from "oslo/oauth2";
import { TimeSpan, createDate } from "oslo";
const authorizeEndpoint = "https://osu.ppy.sh/oauth/authorize";
const tokenEndpoint = "https://osu.ppy.sh/oauth/token";
export class Osu {
    client;
    clientSecret;
    constructor(clientId, clientSecret, options) {
        this.client = new OAuth2Client(clientId, authorizeEndpoint, tokenEndpoint, {
            redirectURI: options?.redirectURI
        });
        this.clientSecret = clientSecret;
    }
    async createAuthorizationURL(state, options) {
        return await this.client.createAuthorizationURL({
            state,
            scopes: options?.scopes ?? []
        });
    }
    async validateAuthorizationCode(code) {
        const result = await this.client.validateAuthorizationCode(code, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
    async refreshAccessToken(refreshToken) {
        const result = await this.client.refreshAccessToken(refreshToken, {
            authenticateWith: "request_body",
            credentials: this.clientSecret
        });
        const tokens = {
            accessToken: result.access_token,
            refreshToken: result.refresh_token,
            accessTokenExpiresAt: createDate(new TimeSpan(result.expires_in, "s"))
        };
        return tokens;
    }
}
