import type { TypedArray } from "../index.js";
import type { KeyPair } from "./index.js";
import type { SHAHash } from "./sha.js";
export type ECDSACurve = "P-256" | "P-384" | "P-521";
export declare class ECDSA {
    private hash;
    private curve;
    constructor(hash: SHAHash, curve: ECDSACurve);
    sign(privateKey: <PERSON><PERSON>yBuffer | TypedArray, data: ArrayBuffer | TypedArray): Promise<ArrayBuffer>;
    verify(publicKey: ArrayBuffer | TypedArray, signature: A<PERSON>yBuffer | TypedArray, data: A<PERSON>yBuffer | TypedArray): Promise<boolean>;
    generateKeyPair(): Promise<KeyPair>;
}
